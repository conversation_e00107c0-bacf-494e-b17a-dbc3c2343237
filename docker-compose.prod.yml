version: '3.8'

services:
  web:
    container_name: najm_web
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers=9 --timeout 560
    volumes:
      - static_volume:/home/<USER>/web/staticfiles
      - media_volume:/home/<USER>/web/mediafiles
      - /develop/najm-files:/home/<USER>/web/najm-files
    ports:
      - "7050:8000"
    env_file:
      - .env.prod
    depends_on:
      - postgres
    links:
      - postgres
    networks:
      - najm

  postgres:
    container_name: najm_db
    ports:
      - "5444:5432"
    restart: unless-stopped
    image: postgres:14.0
    command: >
      postgres -c max_connections=200
               -c shared_buffers=4GB
               -c work_mem=16MB
               -c maintenance_work_mem=512MB
               -c effective_cache_size=12GB
               -c checkpoint_timeout=10min
               -c checkpoint_completion_target=0.9
               -c wal_buffers=16MB


    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.prod
    networks:
      - najm

  najm_redis:
    container_name: najm_redis
    image: redis:alpine
    env_file: .env.prod
    volumes:
      - redis_data:/data
    networks:
      - najm

  najm_celery:
    container_name: najm_celery
    build:
      context: .
      dockerfile: Dockerfile.prod
    env_file: .env.prod
    command: celery -A config worker -l info
    volumes:
      - .:/usr/src/app/
      - static_volume:/home/<USER>/web/staticfiles
      - media_volume:/home/<USER>/web/mediafiles
#      - /develop/najm-files:/home/<USER>/web/najm-files

    depends_on:
      - najm_redis
    networks:
      - najm


  najm_celery-beat:
    container_name: najm_celery_beat
    build:
      context: .
      dockerfile: Dockerfile.prod
    env_file: .env.prod
    command: celery -A config beat -l info
    volumes:
      - .:/usr/src/app/
#      - /develop/najm-files:/home/<USER>/web/najm-files

    depends_on:
      - najm_redis
    networks:
      - najm

  najm_celery_flower:
    container_name: najm_celery_flower
    build:
      context: .
      dockerfile: Dockerfile.prod
    
    command: celery --broker=redis://najm_redis:6379/0 flower
 
    ports:
      - 5550:5555
    env_file:
      - .env.prod
    depends_on:
      - web
      - najm_redis
      - najm_celery

    networks:
      - najm
#    volumes:
#      - /develop/najm-files:/home/<USER>/web/najm-files


volumes:
  postgres_data:
  static_volume:
  media_volume:
  redis_data:

networks:
  najm:
    driver: bridge
