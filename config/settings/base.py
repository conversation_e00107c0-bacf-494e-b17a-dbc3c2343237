from pathlib import Path
import environ
import os
from django_countries.data import COUNTRIES
from split_settings.tools import include
from django.utils.translation import gettext_lazy as _

env = environ.Env(
    # set casting, default value
    # DEBUG=(bool, False)
)

BASE_DIR = Path(__file__).resolve().parent.parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

ALLOWED_HOSTS = env('DJANGO_ALLOWED_HOSTS').split(',')
ALLOWED_HOSTS += ['pay.habibapp.com', ]

SECRET_KEY = 'django-insecure-$77e7e9&4&$kan*jl^5v8htl6b93@afn3%y(8&^)=*xn!u)^2%'

X_FRAME_OPTIONS = 'SAMEORIGIN'

INSTALLED_APPS = [
    'limitless_dashboard.apps.DashboardConfig',
    # 'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sitemaps',
    'rest_framework',
    'rest_framework.authtoken',
    'django.contrib.postgres',
    'drf_yasg',
    "phonenumber_field",
    'django_filters',

    # custom
    'nwh_seo',
    'dj_language',
    'dj_category',
    'ajaxdatatable',
    'dj_filer',
    'apps.account',
    'djongo',
    'apps.localization',
    'rosetta',
    'corsheaders',
    'apps.najm_calendar',
    'apps.hussainiya.album',
    'apps.hussainiya.singers',
    'apps.hussainiya.songs',
    'apps.hussainiya.categories',
    'apps.hussainiya.sliders',
    'apps.hussainiya.genre',
    'apps.hussainiya.publisher',
    'apps.hussainiya.collection',
    'apps.hussainiya.please',
    'apps.hussainiya.provider',

    # 'apps.hussainiya.stats',
    'apps.hussainiya.tags',
    'apps.quran',
    'apps.mafatih',
    'apps.library',
    'apps.ahkam',
    'apps.eightmag',
    'apps.appversion',
    
    #frontapps
    'apps.frontapp',
    'apps.frontapp_v2',

    'apps.habibnet',
    # 'ffmanager',
    'django_countries',
    'apps.hadis',
    'apps.tafsir',
    'apps.tag',
    'apps.aboutservice',
    'apps.adhan',
    'apps.q_and_a',
    'apps.meet',
    'apps.elalhabib',
    'django_crontab',
    'apps.donate',
    'paypal.standard.ipn',
    'apps.report',
    'apps.subscription',
    # 'apps.payment',
    'dynamic_preferences',
    'apps.khatm',
    'apps.habcoin',
    'apps.mafatih_mirror',
    'apps.bookmark',
    'apps.command',
    'apps.notespace',
    'apps.wallet',
    'apps.dua',
    'apps.notification',
    'apps.habit',
]

MIDDLEWARE = [
    'django.middleware.gzip.GZipMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    # 'limitless_dashboard.test_auth_middleware.simple_middleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'config.language_code_middleware.language_middleware',
    'apps.account.test_auth_middleware.simple_middleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        }
    },
]

LANGUAGE_CODE = 'en'

TIME_ZONE = 'Asia/Tehran'

USE_I18N = True

USE_L10N = True

USE_TZ = False

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

STATICFILES_DIRS = [
    BASE_DIR / "static",
    # BASE_DIR / "najm-files",
]

STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "staticfiles"

MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "mediafiles"

FILER_ADMIN_ICON_SIZES = ('32', '48')

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 16,
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'apps.account.auth_back.TokenAuthentication2',
    ],
    # 'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend']

}

AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "apps.account.login_backend.ModelBackendWithUsernameAndEmail",
]

LOCALE_PATHS = [
    str(BASE_DIR / 'apps/hussainiya/songs/locale'),
]

ROSETTA_STORAGE_CLASS = 'rosetta.storage.CacheRosettaStorage'

# custom settings
APPS_REORDER = {
    'auth': {
        'icon': 'icon-shield-check',
        'name': 'Authentication'
    },
    'flatpages': {
        'icon': 'icon-newspaper',
        'name': 'sheets'
    },
    'sites': {
        'icon': 'icon-database',
        'order': 1000
    },
    'hussainiya': {
        'icon': 'mi-brightness-high',
        'name': _('Hussainiya')
    },
    'standard': {
        'name': 'Payments',
        'icon': 'icon-coin-dollar',
    }
}

# User Auth
AUTH_USER_MODEL = "account.User"

# django google recaptcha default keys
RECAPTCHA_PUBLIC_KEY = env('captcha_public_key')
RECAPTCHA_PRIVATE_KEY = env('captcha_private_key')

GEOIP_PATH = str(BASE_DIR / 'geoip_db')
include(str(BASE_DIR / 'apps/*/settings.py'))

ALL_MULTILINGUAL = True
CATEGORY_ENABLE_MULTILINGUAL = True
SLIDER_ENABLE_MULTILINGUAL = True
BLOG_ENABLE_CATEGORY = True

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('POSTGRES_DB'),
        'USER': env('POSTGRES_USER'),
        'PASSWORD': env('POSTGRES_PASSWORD'),
        'HOST': env('POSTGRES_HOST'),
        'PORT': env('POSTGRES_PORT'),
        'CONN_MAX_AGE': 300,
        'ATOMIC_REQUESTS': True,
    },
    # 'mongo': {
    #     'ENGINE': 'djongo',
    #     'NAME': env('MONGO_NAME'),
    #     'CLIENT': {
    #         'host': env('MONGO_HOST'),
    #     },
    # },
    'talk': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('TALK_POSTGRES_DB'),
        'USER': env('TALK_POSTGRES_USER'),
        'PASSWORD': env('TALK_POSTGRES_PASSWORD'),
        'HOST': env('TALK_POSTGRES_HOST'),
        'PORT': env('TALK_POSTGRES_PORT'),
    }
}

PAYPAL_TEST = True

DATABASE_ROUTERS = ['apps.q_and_a.router.TalkDatabaseRouter']

# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.memcached.PyMemcacheCache',
#         # 'LOCATION': '127.0.0.1:11211',
#     }
# }

FCM_API_KEY = env('FCM_API_KEY')

FILER_ENABLE_LOGGING = True
FILER_DEBUG = True
ADMIN_TITLE = 'Habib App'
ADMIN_INDEX_TITLE = 'Habib Administration'

COUNTRIES_ONLY = [
    ("All", " All Countries"),
    *COUNTRIES,
]

COUNTRIES_FIRST = [
    "All",
]
COUNTRIES_FIRST_SORT = True

LANGUAGES = [
    ('ar', _('Arabic')),
    ('az', _('Azerbaijani')),
    ('fr', _('French')),
    ('in', _('Indonesia')),
    ('fa', _('Persian')),
    ('ru', _('Russia')),
    ('es', _('Spanish')),
    ('sw', _('Swahili')),
    ('tr', _('Turkish')),
    ('de', _('German')),
    ('en', _('English')),
    ('fa', _('Persian')),
    ('ur', _('Urdu')),
    ('zh', _('Mandarin')),
    ('zh', _('Chinese')),
    ('he', _('Hebrew')),
    ('he', _('Hebrew')),
    ('bn', _('Bengali')),
    ('ul', _('Urdu_Roman')),
]

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

THUMBNAIL_ALIASES = {
    '': {
        'icon': {'size': (50, 50), 'crop': True},
        'lg': {'size': (1200, 620), 'crop': False},
        'md': {'size': (545, 545), 'crop': False},
        'sm': {'size': (150, 150), 'crop': False},
    },
}

LANGUAGES_SORT_MAP = {
    'az': ['az', 'tr', 'fa'],
    'tr': ['tr', 'az', 'fa'],
    'ru': ['ru', 'az', 'tr', 'fa'],
    'ar': ['ar', 'fa'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'en': ['en', 'fa'],
    'de': ['de', 'en', 'fr', 'es', 'ar'],

    'fr': ['fr', 'en', 'ar', 'fa'],
    'es': ['es', 'en', 'ar', 'fa'],
    'id': ['id', 'en', 'ar', 'fa'],
    'sw': ['sw', 'en', 'ar', 'fa'],
}

LANGUAGES_MAP = {
    'az': ['az', 'tr', 'fa', 'ar'],
    'tr': ['tr', 'az', 'fa', 'ar'],
    'ru': ['ru', 'az', 'tr', 'fa', 'ar'],
    'ar': ['ar', 'fa'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'ul': ['ul','ur', 'en', 'fa', 'ar'],
    'en': ['en', 'ur', 'fa', 'ar'],
    'de': ['de', 'en', 'fr', 'es', 'ar'],
    'fa': ['fa', 'az', 'ar', 'en', 'ur'],

    'fr': ['fr', 'en', 'ar', 'fa'],
    'es': ['es', 'en', 'ar', 'fa'],
    'id': ['id', 'en', 'ar', 'fa'],
    'sw': ['sw', 'en', 'ar', 'fa'],
}

FILE_UPLOAD_HANDLERS = [
    'django.core.files.uploadhandler.TemporaryFileUploadHandler',
]

REDIS_URL="redis://najm_redis:6379/1"


HABIBCOIN_NUMBER = 15
HABIBCOIN_VALUE = 1.5

LIVEKIT_API_KEY="APIoHieb3ZapyVZ"
LIVEKIT_API_SECRET="8wHYKSalVEZ07ziPUCAKHASJIdfcWFDGLACLB22XkNZ"

AUTH_METHOD = "phone" # phone or third_party 
VERIFICATION_TIME_LIMIT = 4
IS_SEND_OTP = True
VERIFICATION_METHOD = "WHATSAPP" # WHATSAPP OR SMS