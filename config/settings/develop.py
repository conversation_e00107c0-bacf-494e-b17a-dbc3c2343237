from .base import *

DJANGO_REDIS_IGNORE_EXCEPTIONS = True
DEBUG = True

CORS_ALLOW_ALL_ORIGINS = True

# CACHES = {
    # 'default': {
        # "BACKEND": "django.core.cache.backends.dummy.DummyCache",
    # },
    # 'memory': {
        # 'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        # 'LOCATION': 'unique-snowflake',
        # 'TIMEOUT': 5000,
    # },
# }

REDUCE_AUDIO_SIZE = env("REDUCE_AUDIO_SIZE")

PAYPAL_ACCOUNT = '<EMAIL>'
PAYPAL_TOKEN = 'access_token$sandbox$6w4mbw6phbdn7bjy$********************************'

# API Endpoints.
POSTBACK_ENDPOINT = "https://ipnpb.paypal.com/cgi-bin/webscr"
SANDBOX_POSTBACK_ENDPOINT = "https://ipnpb.sandbox.paypal.com/cgi-bin/webscr"

# Login endpoints
LOGIN_URL = "https://www.paypal.com/cgi-bin/webscr"
SANDBOX_LOGIN_URL = "https://www.sandbox.paypal.com/cgi-bin/webscr"

PAYMENT_HOST = 'localhost:8000'
PAYMENT_MODEL = 'payment.Payment'

# Whether to use TLS (HTTPS). If false, will use plain-text HTTP.
# Defaults to ``not settings.DEBUG``.
PAYMENT_USES_SSL = False

BROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 3600}
BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_BROKER_TRANSPORT = 'redis'

PAYMENT_VARIANTS = {
    'default': ('payments.dummy.DummyProvider', {}),
    'paypal': (
        'payments.paypal.PaypalProvider',
        {
            'client_id': 'AdMEpb7MvQOmIZy8SdprUrbJeRZXudklHuboSvGiPe0_4uX9YTDhIYjBH4rJn_GrqX7y61QN_OhfjZjT',
            'secret': 'ENd1Hwih1nuf48-aFdT83wla89RzRvJ5ZBFOkHZK13ZHFyc-YY9DWEWG4CdnhThnPmEDDObheXbIYCBk',
            'endpoint': 'https://api.sandbox.paypal.com',
            'capture': False,
        },
    ),
    'stripe': (
        'payments.stripe.StripeProvider',
        {
            'secret_key': 'sk_test_51OJumCGbRu7JdMKHMa8xd7qOwJwJjqKg7sNhjjUaopWLrbE3qxwvB5ZzssRPbDPdMKDbKxDb2OO4TlkaIZJoenwI00P1Up6VaX',
            'public_key': 'pk_test_51OJumCGbRu7JdMKHfKv3K8uTBexUNZsE04gGZPLa4EmJKimyjwWuuzWgR4CVUCJEMuwA6g9wwZobf0goNF2hJcVb00BFEcIIYA',
        }
    )
}
