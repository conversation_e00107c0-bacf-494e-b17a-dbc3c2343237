from django.http import HttpResponse

ALLOWED_URLS = [
    "/login", "/admin", "telegram-sentry", 'bot-runner', "auth/google/", "/elalhabib/submit/", '/pay', 'paypal', "/web/",
    'robots.txt', "/.well-known/", "about", "/download", 'dont-kill/', 'audios/', "web-donate", "delete_account", '/khatm',
    '/languages/', '/meets/', 'auth/user/region/'
]


def language_middleware(get_response):
    def middleware(request):
        if not request.user.is_authenticated:
            if ":8000" not in request.get_host():
                if request.path != '/' and '/policy/' not in request.path:
                    if all([u not in request.path for u in ALLOWED_URLS]):
                        if "dart:io" not in request.headers.get('user-agent', ''):
                            return HttpResponse("Api Call not allowed", status=401)

        request.LANGUAGE_CODE = request.GET.get('language_code') or request.META.get('HTTP_X_USER_LANGUAGE') or request.LANGUAGE_CODE

        platform = (request.headers.get('platform') or '').lower()
        user_agent = (request.headers.get('User-Agent') or '').lower()
        
        request.is_ios = (
            'iphone' in user_agent or
            'ipad' in user_agent or
            'ios' in user_agent or
            platform == 'ios'
        )
                            
        response = get_response(request)

        return response

    return middleware
