version: '3.8'

services:
  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:./
      - ./volumes/static_data:/usr/src/app/static/
    ports:
      - "9000:8000"
    env_file:
      - .env.dev
    depends_on:
      - postgres

    networks:
      - najm


  postgres:
    ports:
      - "5444:5432"
    image: postgres:13.7
    volumes:
      - ./volumes/postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.dev
    networks:
      - najm


  najm_redis:
    container_name: najm_redis
    image: redis:alpine
    env_file: .env.dev
    volumes:
      - redis_data:/data
    networks:
      - najm

  najm_celery:
    container_name: najm_celery
    build: .
    env_file: .env.dev
    command: celery -A config worker -l info
    volumes:
      - .:/usr/src/app/
    depends_on:
      - najm_redis
    networks:
      - najm


  najm_celery-beat:
    container_name: najm_celery_beat
    build: .
    env_file: .env.dev
    command: celery -A config beat -l info
    volumes:
      - .:/usr/src/app/
    depends_on:
      - najm_redis
    networks:
      - najm

  najm_celery_flower:
    container_name: najm_celery_flower
    build: .
    command: celery flower persistent=True state_save_interval=100 -A config --port=5555 --broker=redis://najm_redis:6379/0
    ports:
      - 5555:5555
    env_file:
      - .env.dev
    depends_on:
      - web
      - najm_redis
      - najm_celery

    networks:
      - najm


volumes:
  postgres_data:
  staticfiles:
  redis_data:


networks:
  najm:
