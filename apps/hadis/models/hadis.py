from django.db import models
from django.utils.translation import gettext_lazy as _
from limitless_dashboard.fields.comma_sep import CommaSepModelField
from django.contrib.postgres.fields import ArrayField
from django.contrib.postgres.indexes import GinIndex
from filer.fields.image import FilerImageField


class HadisTag(models.Model):
    tag = models.ForeignKey("tag.Tag", on_delete=models.CASCADE, verbose_name=_('tag'))
    hadis = models.ForeignKey("Hadis", on_delete=models.CASCADE, verbose_name=_('hadis'))
    level = models.PositiveSmallIntegerField(default=1, verbose_name=_('level'))

    class Meta:
        unique_together = ('tag', 'hadis', 'level')
        db_table = 'hadis_hadis_tags'
        indexes = [
            models.Index(fields=['tag']),  
            models.Index(fields=['hadis']),  
        ]        
    def __str__(self):
        return f"{self.tag.title}"


class Narrates(models.Model):
    name = models.CharField(max_length=255, verbose_name=_('name'))
    photo = models.ImageField(verbose_name=_('photo'), null=True, blank=True, upload_to='hadis/photos')
    translations = models.JSONField(verbose_name=_('translations'), default=dict)
    order = models.PositiveSmallIntegerField(default=50, verbose_name=_('order'))

    def __str__(self):
        return self.name

    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['text']

        for tr in self.translations:
            if tr['language_code'] == 'en':
                return tr['text']

        return str(self)

    class Meta:
        ordering = ('order',)
        verbose_name = _('narrator')
        verbose_name_plural = _('narrators')
        indexes = [
            models.Index(fields=['order']),  
        ]

class Reference(models.Model):
    title = models.CharField(max_length=255, verbose_name=_('title'))
    translations = models.JSONField(verbose_name=_('translations'), default=dict)
    description = models.JSONField(verbose_name=_('description'), default=dict)
    thumbnail = FilerImageField(
        related_name='+', on_delete=models.PROTECT, null=True, blank=True,
        verbose_name=_('thumbnail')
    )
    
    
    def __str__(self):
        return self.title

    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['text']

        return None
    
    def get_description(self, lang):
        for tr in self.description:
            if tr['language_code'] == lang:
                return tr['description']
        return None
        

    class Meta:
        verbose_name = _('reference')
        verbose_name_plural = _('references')
        indexes = [
            models.Index(fields=['title']),  
        ]

class Hadis(models.Model):
    number = models.PositiveIntegerField(verbose_name=_('number'), unique=True)
    text = models.TextField(verbose_name=_('text'))
    translations = models.JSONField(verbose_name=_('translation'), default=dict)
    tags = models.ManyToManyField("tag.Tag", verbose_name=_('tag'), blank=True, through=HadisTag)
    narrated_by = models.ForeignKey(
        Narrates, verbose_name=_('narrated by'), on_delete=models.CASCADE, null=True, blank=True
    )
    notif_dates = CommaSepModelField(
        null=True, blank=True, help_text=_('in Lunar format Month/Day ( 05/12 ) press enter or ( , ) to apply item'),
    )
    meta_data = models.JSONField(verbose_name=_('meta data'), default=dict)

    narrates_series = CommaSepModelField(
        verbose_name=_('narrates series'), null=True, blank=True, help_text=_('press enter or ( , ) to apply item')
    )
    status = models.BooleanField(default=True, verbose_name=_('visibility'), )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    category = models.ForeignKey("hadis.Category", null=True, on_delete=models.SET_NULL)

    def __str__(self):
        return f"<{self.number}> {self.text[:32]}"

    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['text']

        for tr in self.translations:
            if tr['language_code'] == 'en':
                return tr['text']

        return None

    class Meta:
        verbose_name = _('hadis')
        verbose_name_plural = _('hadises')
        indexes = [
            models.Index(fields=['status']),  
            models.Index(fields=['category']),  
            GinIndex(fields=['meta_data'], name='meta_data_gin_idx'),  

        ]

class ReferenceAddr(models.Model):
    hadis = models.ForeignKey("Hadis", verbose_name=_('hadis'), on_delete=models.CASCADE)
    book = models.ForeignKey(Reference, verbose_name=_('references'), null=True, related_name='books',
                             on_delete=models.CASCADE)
    ref_doc_numb = models.CharField(max_length=64, verbose_name=_('vol'), null=True, blank=True)
    ref_doc_page = models.CharField(max_length=64, verbose_name=_('page'), null=True, blank=True)
    ref_hadis_num = models.CharField(max_length=64, verbose_name=_('number'), null=True, blank=True)
    ref_text = models.CharField(max_length=200, verbose_name=_('text'), null=True, blank=True)

    def __str__(self):
        return str(self.book)

    class Meta:
        indexes = [
            models.Index(fields=['hadis']),  
            models.Index(fields=['book']),  
        ]