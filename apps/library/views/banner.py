from django.db.models import OuterRef, Count, Q, Avg
from django.contrib.postgres.aggregates import <PERSON><PERSON>yAgg  
from rest_framework.generics import ListAPIView, get_object_or_404
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.library.models import Collection, UserRate, Book, BookFile
from apps.library.serializers.banner import BannerListSerializer
from apps.library.serializers.book import BookListSerializer, BookListV2Serializer


class BannerListView(ListAPIView):
    serializer_class = BannerListSerializer
    permission_classes = (IsAuthenticated,)
    pagination_class = None

    def get_queryset(self):
        _query = Q(status=True, pin_top=True)

        return Collection.objects.filter(
            _query,
            title__contains=[{'language_code': self.request.LANGUAGE_CODE}],
        ).order_by('-priority', '-id', )


class BannerBottomListView(ListAPIView):
    serializer_class = BannerListSerializer
    permission_classes = (IsAuthenticated,)
    pagination_class = None

    def get_queryset(self):
        _query = Q(status=True, pin_down=True)

        return Collection.objects.filter(
            _query,
            title__contains=[{'language_code': self.request.LANGUAGE_CODE}],
        ).order_by('-priority', '-id', )


class BannerDetailView(ListAPIView):
    serializer_class = BookListSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        collection = get_object_or_404(Collection, pk=self.kwargs['pk'])
        qs = collection.get_books().annotate(
            avg_rate=Avg('rates__rate'),
        )
        if self.request.user.is_authenticated:
            qs = qs.annotate(
                user_rate=UserRate.objects.filter(book=OuterRef('pk'), user=self.request.user).values('rate')[:1],
                is_bookmark=Count('bookmark', filter=Q(bookmark__user=self.request.user))
            )

        return qs


class BannerDetailV2View(ListAPIView):
    serializer_class = BookListV2Serializer
    permission_classes = (IsAuthenticated,)

    @swagger_auto_schema(
        operation_summary="Redesign v2",
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        collection = get_object_or_404(Collection, pk=self.kwargs['pk'])
        valid_file_types = [BookFile.FileType.pdf, BookFile.FileType.epub, BookFile.FileType.audio]
        
        qs = collection.get_books().annotate(
            avg_rate=Avg('rates__rate'),
            file_types=ArrayAgg('files__type', distinct=True, filter=Q(files__type__in=valid_file_types))

        )

        if self.request.user.is_authenticated:
            qs = qs.annotate(
                user_rate=UserRate.objects.filter(book=OuterRef('pk'), user=self.request.user).values('rate')[:1],
                is_bookmark=Count('bookmark', filter=Q(bookmark__user=self.request.user)),
            )

        return qs
