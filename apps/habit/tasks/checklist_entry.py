import time
from datetime import datetime, timedelta
from celery import shared_task
from django.db import transaction
from django.db.models import OuterRef, Exists
from django.utils import timezone

from apps.habit.models import UserChecklistItem, ChecklistEntry



@shared_task(bind=True, name='habit.generate_checklist_entries')
def generate_checklist_entries(self):
    """
    تولید Entry های چک‌لیست به صورت بهینه و مرحله‌ای
    """
    try:
        today = timezone.now().date()
        current_time = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"🚀 Starting optimized checklist entry generation for {today} at {current_time}")

        # محاسبه روزهای هدف
        days_to_generate = [today + timedelta(days=i) for i in range(4)]

        # تنظیمات بهینه‌سازی
        config = {
            'batch_size': 500,           # کاهش batch size برای کم کردن memory
            'max_items_per_run': 2000,   # حداکثر آیتم در هر اجرا
            'sleep_between_batches': 0.1, # استراحت بین batch ها
            'transaction_batch_size': 100 # تعداد Entry در هر transaction
        }

        total_created = 0

        # پردازش هر روز جداگانه
        for day_index, target_date in enumerate(days_to_generate):
            day_start_time = timezone.now().strftime("%H:%M:%S")
            print(f"\n📅 Processing day {day_index + 1}/4: {target_date} at {day_start_time}")

            try:
                created_for_day = process_single_day_optimized(target_date, config)
                total_created += created_for_day

                # استراحت بین روزها برای کاهش فشار سرور
                if day_index < len(days_to_generate) - 1:  # نه آخرین روز
                    print(f"   💤 Resting 2 seconds before next day...")
                    time.sleep(2)

            except Exception as e:
                error_msg = f"Error processing {target_date}: {str(e)}"
                print(f"   ❌ {error_msg}")
                # ادامه با روز بعدی
                continue

        end_time = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n🎉 Completed! Total entries created: {total_created} at {end_time}")
        return total_created

    except Exception as e:
        # خطای کلی Task
        error_msg = f"Critical error in checklist generation: {str(e)}"
        print(f"❌ {error_msg}")

        # Retry task اگر امکان دارد (فقط در Celery)
        if self and hasattr(self, 'request') and self.request.retries < 3:
            print(f"🔄 Retrying task... (attempt {self.request.retries + 1}/3)")
            raise self.retry(countdown=60, max_retries=3)
        else:
            print(f"💥 Task failed")
            raise


def process_single_day_optimized(target_date, config):
    """
    پردازش بهینه یک روز مشخص
    """
    target_day_name = target_date.strftime("%A").lower()
    print(f"   🔍 Processing {target_day_name}...")

    # شمارش کل آیتم‌های فعال
    total_active_items = UserChecklistItem.objects.filter(
        is_active=True,
        is_deleted=False,
        custom_days__contains=target_day_name
    ).count()

    print(f"   📊 Total active items for {target_day_name}: {total_active_items:,}")

    if total_active_items == 0:
        print(f"   ✅ No active items for {target_day_name}")
        return 0

    # محدود کردن تعداد آیتم‌ها در هر اجرا
    max_items = config['max_items_per_run']
    if total_active_items > max_items:
        print(f"   ⚠️  Limiting to {max_items:,} items (out of {total_active_items:,})")
        print(f"   💡 Run task multiple times to process all items")

    # پردازش به صورت chunk های کوچک
    total_created = 0
    processed_items = 0

    chunk_size = config['batch_size']

    for offset in range(0, min(total_active_items, max_items), chunk_size):
        try:
            created_in_chunk = process_chunk_optimized(
                target_date, target_day_name, offset, chunk_size, config
            )
            total_created += created_in_chunk
            processed_items += chunk_size

            # نمایش پیشرفت
            progress = min(processed_items, max_items)
            print(f"   📈 Progress: {progress:,}/{min(total_active_items, max_items):,} items processed")

            # استراحت بین chunk ها
            if created_in_chunk > 0:
                time.sleep(config['sleep_between_batches'])

        except Exception as e:
            print(f"   ❌ Error in chunk {offset}: {str(e)}")
            continue

    day_end_time = timezone.now().strftime("%H:%M:%S")
    print(f"   ✅ Day completed: {total_created:,} entries created at {day_end_time}")
    return total_created


def process_chunk_optimized(target_date, target_day_name, offset, chunk_size, config):
    """
    پردازش بهینه یک chunk از آیتم‌ها
    """
    # ابتدا فیلتر کنیم، سپس slice
    base_query = UserChecklistItem.objects.filter(
        is_active=True,
        is_deleted=False,
        custom_days__contains=target_day_name
    ).select_related('user')

    # فیلتر آیتم‌هایی که Entry ندارند
    existing_subquery = ChecklistEntry.objects.filter(
        user_checklist_item=OuterRef('pk'),
        date=target_date
    )

    items_needing_entries = base_query.annotate(
        has_entry=Exists(existing_subquery)
    ).filter(has_entry=False)

    # حالا slice کنیم
    chunk_items = items_needing_entries[offset:offset + chunk_size]
    items_list = list(chunk_items)

    if not items_list:
        return 0

    # ایجاد Entry ها در transaction های کوچک
    total_created = 0
    transaction_size = config['transaction_batch_size']

    for i in range(0, len(items_list), transaction_size):
        transaction_batch = items_list[i:i + transaction_size]

        try:
            with transaction.atomic():
                new_entries = [
                    ChecklistEntry(
                        user_checklist_item=item,
                        user=item.user,
                        date=target_date,
                        completed=False
                    ) for item in transaction_batch
                ]

                created_entries = ChecklistEntry.objects.bulk_create(
                    new_entries,
                    ignore_conflicts=True
                )

                created_count = len(created_entries)
                total_created += created_count

        except Exception as e:
            print(f"     ⚠️ Transaction failed for batch {i}: {str(e)}")
            continue

    return total_created
  
    
if __name__ == "__main__":
    print("Executed at:", datetime.now())
    generate_checklist_entries()