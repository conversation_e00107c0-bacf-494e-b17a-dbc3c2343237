from rest_framework import serializers

from dj_filer.admin import get_thumbs

from apps.habit.models import Challenge, ChallengeParticipant



class BaseChallengeSerializer(serializers.ModelSerializer):
    participant_count = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    summary = serializers.SerializerMethodField()
    is_participant = serializers.SerializerMethodField()

    class Meta:
        model = Challenge
        fields = ['id', 'title', 'image', 'summary', 'is_unlimited', 'duration_days', 'status', 'created_at', 'participant_count', 'is_participant']

    def get_participant_count(self, obj):
        return obj.participant_count

    def get_image(self, obj):
        return get_thumbs(obj.image, self.context.get('request'))

    def get_title(self, obj):
        request = self.context.get('request')
        lang = request.LANGUAGE_CODE
        return obj.get_field_translation(lang, obj.title)

    def get_summary(self, obj):
        request = self.context.get('request')
        lang = request.LANGUAGE_CODE
        return obj.get_field_translation(lang, obj.summary)

    def get_is_participant(self, obj):
        user = self.context['request'].user
        return ChallengeParticipant.objects.filter(challenge=obj, user=user).exists()


class ChallengeListSerializer(BaseChallengeSerializer):
    class Meta(BaseChallengeSerializer.Meta):
        fields = BaseChallengeSerializer.Meta.fields


class ChallengeDetailSerializer(BaseChallengeSerializer):
    description = serializers.SerializerMethodField()

    class Meta(BaseChallengeSerializer.Meta):
        fields = BaseChallengeSerializer.Meta.fields + ['description']

    def get_description(self, obj):
        request = self.context.get('request')
        lang = request.LANGUAGE_CODE
        return obj.get_field_translation(lang, obj.description)
    
    
class ChallengeJoinSerializer(serializers.Serializer):
    challenge_id = serializers.IntegerField()

    def validate_challenge_id(self, value):
        if not Challenge.objects.filter(id=value).exists():
            raise serializers.ValidationError("Challenge with this ID does not exist.")
        return value