

from rest_framework import serializers
from apps.habit.models import UserChecklistItem, ChecklistDetail



class UserChecklistItemSerializer(serializers.ModelSerializer):
    title = serializers.CharField(required=False, read_only=True)  
    custom_days = serializers.ListField(required=False)
    has_details = serializers.SerializerMethodField() 
    task_type = serializers.SerializerMethodField()
    is_active = serializers.BooleanField(required=False)

    class Meta:
        model = UserChecklistItem
        fields = ['id', 'title', 'is_active', 'custom_days', 'has_details', 'task_type',]


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['title'] = self.get_title(instance)  # title را از get_title میگیریم
        representation['custom_days'] = self.get_custom_days(instance)
        return representation

    def to_internal_value(self, data):
        if 'custom_days' in data and isinstance(data['custom_days'], str):
            data['custom_days'] = [data['custom_days']]
        if 'title' in data:
            data['custom_title'] = data.pop('title')  # تغییر داده شده: حذف None
        return super().to_internal_value(data)
    
                
    def get_title(self, obj):
        request = self.context.get('request')
        lang = request.LANGUAGE_CODE

        if obj.template_item:
            return obj.template_item.get_title(lang)
        return obj.custom_title
    
    def get_custom_days(self, obj):
        return list(obj.custom_days) if obj.custom_days else []
    
    def get_has_details(self, obj):
        if obj.template_item:
            return ChecklistDetail.objects.filter(checklist=obj.template_item).exists()
        return False  
    
    def get_task_type(self, obj):
        if obj.template_item:
            return obj.template_item.task_type
        return "custom"    
    
    
class ChecklistDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChecklistDetail
        fields = ['id', 'content', 'language', 'created_at', 'updated_at']
        
        
class ChecklistEntryListSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()
    completed = serializers.BooleanField()
    has_details = serializers.BooleanField(default=False)

class ChecklistStatsSerializer(serializers.Serializer):
    total_days = serializers.IntegerField()
    completed_days = serializers.IntegerField()
    percentage = serializers.FloatField()
    
    