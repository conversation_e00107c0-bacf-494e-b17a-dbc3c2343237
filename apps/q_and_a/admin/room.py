from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.db.models import Count
from django import forms
from utils.json_editor_field import JsonEditorWidget
from django.utils.translation import gettext_lazy as _

from apps.q_and_a.models import *
    
    


        
class UserFilter(admin.SimpleListFilter):
    parameter_name = 'user__username'
    title = "User"

    def lookups(self, request, model_admin):
        return (
            ('1', 'Empty'),  # Filter by empty values
            ('0', 'Not Empty'),  # Filter by non-empty values
        )

    def queryset(self, request, queryset):
        if username := request.GET.get('user__username', None):
            return queryset.filter(client__username=username)

        return queryset


class MessageInline(admin.TabularInline):
    model = Messages
    fields = (
        'by_user_type', 'content', 'at_time', 'has_read',
    )

    def has_add_permission(self, request, obj):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(Rooms)
class RoomAdmin(AjaxDatatable):
    list_display = ('_consultant', '_client', 'created_at', '_messages')
    list_filter = (
        'consultant', UserFilter
    )
    search_fields = ('client__username', 'consultant__username')
    inlines = [
        MessageInline,
    ]

    fields = (
        'consultant', 'client', 'created_at',
    )

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    @admin.display(description='Consultant', ordering='consultant')
    def _consultant(self, obj):
        return obj.consultant.username

    @admin.display(description='Client', ordering='client')
    def _client(self, obj):
        return obj.client.username

    @admin.display(description='Messages', ordering='messages_count')
    def _messages(self, obj):
        return obj.messages_count

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related(
            'consultant', 'client',
        ).annotate(
            messages_count=Count('messages')
        ).filter(
            messages_count__gt=0
        )



@admin.register(Calls)
class CallsAdmin(AjaxDatatable):
    list_display = ('consultant', 'client', 'call_type', 
                   'cost', 'timer_active', 'status', 'is_reservation', 'reservation_date', 'start_time', 'end_time')
    list_filter = ('call_type', 'status', 'is_reservation')
    search_fields = ('consultant__id', 'client__id')
    date_hierarchy = 'start_time'
    readonly_fields = ('id',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('consultant', 'client', 'call_type', 'cost')
        }),
        ('Time Information', {
            'fields': ('start_time', 'end_time', 'timer_active')
        }),
        ('Status', {
            'fields': ('status',)
        }),
        ('Reservation', {
            'fields': ('is_reservation', 'reservation_date')
        }),
    )