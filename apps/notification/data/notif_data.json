[{"navigate": "<PERSON><PERSON><PERSON>", "key": "hussainiyah_main", "name": "<PERSON><PERSON><PERSON> Main", "help": {"text": "حسینیه داخل البوم با ایدی البوم", "url": null}, "data": {"data": null, "navigation": "/HussainiyahMainPage", "model": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"navigate": "<PERSON><PERSON><PERSON>", "key": "hussainiyah_event", "name": "Hussainiyah Event", "help": {"text": "حسینیه داخل البوم با ایدی البوم", "url": "https://habibapp.com/hussainya/v2/albums/2312/"}, "data": {"data": "2312", "navigation": "/HussainiyahEventPage", "model": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"navigate": "<PERSON><PERSON><PERSON>", "key": "hussain<PERSON><PERSON>_song", "name": "<PERSON><PERSON><PERSON>", "help": {"text": "حسینیه داخل آهنگ با اسلاگ آهنگ", "url": "https://habibapp.com/hussainya/songs/sasc2/"}, "data": {"data": "sasc2", "navigation": "/HussainiyahSongPage", "model": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"navigate": "<PERSON><PERSON><PERSON>", "key": "huss<PERSON><PERSON><PERSON>_singer", "name": "<PERSON><PERSON><PERSON>", "help": {"text": "حسینیه داخل خواننده با اسلاگ خواننده", "url": "https://habibapp.com/hussainya/v2/singer/sasc2/"}, "data": {"data": "sasc2", "navigation": "/HussainiyahSingerPage", "model": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"navigate": "<PERSON><PERSON><PERSON>", "key": "hussain<PERSON>h_live", "name": "<PERSON><PERSON><PERSON> Live", "help": {"text": "حسینیه لیست لایو ها اگر دیتا ایدی داشت سینگل لایو بیاد", "url": "https://habibapp.com/hussainyah/lives/"}, "data": {"data": null, "navigation": "/HussainiyahLivePage", "model": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"navigate": "<PERSON><PERSON><PERSON>", "key": "hussainiyah_please", "name": "<PERSON><PERSON><PERSON>", "help": {"text": "حسینیه لیست داخل بفرمایید روضه", "url": null}, "data": {"data": null, "navigation": "/HussainiyahPleasePage", "model": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"navigate": "Library", "key": "library_main", "name": "Library Main", "help": {"text": "سرویس کتابخانه صفحه اصلی", "url": null}, "data": {"data": null, "navigation": "/LibraryMainPage", "model": "library"}}, {"navigate": "Library", "key": "library_book", "name": "Library Book", "help": {"text": " سرویس کتابخانه داخل کتاب با اسلاگ کتاب", "url": "https://habibapp.com/library/v2/books/slugbook213/"}, "data": {"data": "slugbook213", "navigation": "/LibraryBookPage", "model": "library"}}, {"navigate": "Library", "key": "library_downloads", "name": "Library Downloads", "help": {"text": " سرویس کتابخانه لیست دانلود های کاربر", "url": "https://habibapp.com/library/books/user-downloads/"}, "data": {"data": null, "navigation": "/LibraryDownloadPage", "model": "library"}}, {"navigate": "Talk", "key": "talk_main", "name": "Talk Main", "help": {"text": "سرویس تاک صفحه اصلی ", "url": null}, "data": {"data": null, "navigation": "/TalkMainPage", "model": "talk"}}, {"navigate": "Talk", "key": "talk_consultant", "name": "Talk Consultant", "help": {"text": " سرویس تاک داخل صفحه مشاور با یوزرنیم مشاور", "url": null}, "data": {"data": "<EMAIL>", "navigation": "/TalkConsultantPage", "model": "talk"}}, {"navigate": "Meet", "key": "meet_main", "name": "Meet Main", "help": {"text": " سرویس میت صفحه اصلی", "url": null}, "data": {"data": null, "navigation": "/MeetMainPage", "model": "meet"}}, {"navigate": "Meet", "key": "meet_room", "name": "Meet Room", "help": {"text": " سرویس میت داخل اتاق با ایدی اگر اتاق لایو بود هم بریم داخل آن", "url": null}, "data": {"data": "213", "navigation": "/MeetRoomPage", "model": "meet"}}, {"navigate": "Quran", "key": "quran_main", "name": "Quran Main", "help": {"text": " سرویس قرآن صفحه اصلی", "url": null}, "data": {"data": null, "navigation": "/QuranMainPage", "model": "quran"}}, {"navigate": "Quran", "key": "quran_page", "name": "<PERSON> Page", "help": {"text": " سرویس قرآن داخل صفحه با شماره صفحه", "url": null}, "data": {"data": "2", "navigation": "/QuranPagePage", "model": "quran"}}, {"navigate": "Quran", "key": "quran_verse", "name": "Quran Verse", "help": {"text": " سرویس قرآن آیه با شماره آیه در کل قران برای سوره هم میتوانیم اولین شماره سوره را جایگذاری کنیم", "url": null}, "data": {"data": "2232", "navigation": "/QuranDetailPage", "model": "quran"}}, {"navigate": "Quran", "key": "quran_khatm_individual", "name": "Quran Khatm Individual", "help": {"text": " سرویس قرآن لیست ختم های شخصی", "url": null}, "data": {"data": null, "navigation": "/QuranKhatmIndividualPage", "model": "quran"}}, {"navigate": "Quran", "key": "quran_khatm_group", "name": "Quran Khatm Group", "help": {"text": " سرویس قرآن لیست ختم گروهی", "url": null}, "data": {"data": null, "navigation": "/QuranKhatmGroupPage", "model": "quran"}}, {"navigate": "<PERSON><PERSON><PERSON><PERSON>", "key": "duas_main", "name": "<PERSON><PERSON>", "help": {"text": "  سرویس مفاتیح صفحه اصلی اگر دیتامقدار داشت میره به همان دعا", "url": null}, "data": {"data": "23", "navigation": "/DuaMainPage", "model": "duas"}}, {"navigate": "<PERSON><PERSON><PERSON><PERSON>", "key": "duas_famous", "name": "<PERSON>as Famous", "help": {"text": "  سرویس مفاتیح تب دعا های معروف", "url": null}, "data": {"data": null, "navigation": "/DuaFamousPage", "model": "duas"}}, {"navigate": "<PERSON><PERSON><PERSON><PERSON>", "key": "duas_zekrcount", "name": "<PERSON><PERSON>", "help": {"text": "  سرویس مفاتیح تب شمارش ذکر", "url": null}, "data": {"data": null, "navigation": "/DuaZekrPage", "model": "duas"}}, {"navigate": "<PERSON><PERSON>", "key": "hadis_main", "name": "<PERSON><PERSON>", "help": {"text": "  سرویس حدیث صفحه اصلی ", "url": null}, "data": {"data": null, "navigation": "/HadisMainPage", "model": "hadis"}}, {"navigate": "<PERSON><PERSON>", "key": "hadis_daily", "name": "Hadis Daily", "help": {"text": "  سرویس حدیث صفحه دیلی ", "url": null}, "data": {"data": null, "navigation": "/HadisDailyPage", "model": "hadis"}}, {"navigate": "Ahka<PERSON>", "key": "ahkam_main", "name": "Ahkam Main", "help": {"text": "  سرویس احکام صفحه اصلی ", "url": null}, "data": {"data": null, "navigation": "/AhkamMainPage", "model": "ahkam"}}, {"navigate": "Ahka<PERSON>", "key": "ahkam_detail", "name": "Ahkam Detail", "help": {"text": "  سرویس احکام صفحه جزعیات ", "url": "https://habibapp.com/masaels/v2/"}, "data": {"data": "1176", "navigation": "/AhkamDetailPage", "model": "ahkam"}}, {"navigate": "Calendar", "key": "calendar_main", "name": "Calendar", "help": {"text": " تقویم صفحه اصلی ", "url": null}, "data": {"data": null, "navigation": "/CalenderMainPage", "model": "calender"}}, {"navigate": "Calendar", "key": "calendar_event_detail", "name": "Calendar Event Detail", "help": {"text": " تقویم صفحه جزعیات مناسبت با ایدی ایوینت (به صورت html است)", "url": "https://habibapp.com/calendar/occasions/12/"}, "data": {"data": "23", "navigation": "/CalenderDetailPage", "model": "calender"}}, {"navigate": "Habib Coin", "key": "coin_main", "name": "Coin Main", "help": {"text": " ح<PERSON><PERSON><PERSON> کوین صفحه اصلی", "url": null}, "data": {"data": null, "navigation": "/CoinMainPage", "model": "coin"}}, {"navigate": "Habib Coin", "key": "coin_payment", "name": "Coin Payment", "help": {"text": " ح<PERSON><PERSON><PERSON> کوین صفحه لیست پکیج ها", "url": null}, "data": {"data": null, "navigation": "/CoinPayPage", "model": "coin"}}, {"navigate": "Habib Coin", "key": "coin_used", "name": "Coin Used", "help": {"text": " ح<PERSON><PERSON><PERSON> کوین صفحه لیست کوین های استفاده شده", "url": null}, "data": {"data": null, "navigation": "/CoinUsedPage", "model": "coin"}}, {"navigate": "Habib Coin", "key": "coin_transaction", "name": "Coin Transaction", "help": {"text": " ح<PERSON><PERSON><PERSON> کوین صفحه لیست تراکنش های کاربر", "url": null}, "data": {"data": null, "navigation": "/CoinTransactionPage", "model": "coin"}}, {"navigate": "Wallet", "key": "wallet_main", "name": "Wallet Main", "help": {"text": " ک<PERSON><PERSON> پول صفحه اصلی", "url": null}, "data": {"data": null, "navigation": "/WalletMainPage", "model": "wallet"}}, {"navigate": "Global", "key": "global_eightmag", "name": "Global Eightmag", "help": {"text": " عمومی - وقتی کلیک شد باید با اسلاگ منقل شود به صفحه ", "url": "https://habibapp.com/eightmag-details/214axc/"}, "data": {"data": "214axc", "navigation": "/GlobalPage", "model": "global"}}, {"navigate": "Notification", "key": "notification_main", "name": "Notification Main", "help": {"text": " لیست نوتفیکیشن ها ", "url": null}, "data": {"data": null, "navigation": "/NotificationPage", "model": "notification"}}, {"navigate": "Notification", "key": "notification_url", "name": "Notification URL", "help": {"text": " بازکردن لیست", "url": null}, "data": {"data": null, "navigation": null, "model": null}}]