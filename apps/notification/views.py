from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from django.shortcuts import get_object_or_404
from apps.notification.serializers import ReminderSerializer, ReminderUpdateSerializer, UserNotificationSerializer
from apps.notification.models import <PERSON>ush<PERSON><PERSON><PERSON>, <PERSON>mind<PERSON>, UserNotification
from apps.account.fcm_notification import send_notification, send_silent_notification, send_custom_payload_notification
from apps.account.models import User

import logging
logger = logging.getLogger(__name__)


class PushMessagesAllReadAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['notification'],
        manual_parameters=[
            openapi.Parameter(
                'service',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Specify the service to filter messages (optional). If omitted, all messages for the user will be marked as read.",
                enum=[
                    'hussainiya', 'library', 'talk', 'meet', 'mafatih', 
                    'hadis', 'khatm', 'quran', 'tafsir', 'ahkam', 'coin', 'global', 'null'
                ]
            ),
            openapi.Parameter(
                'value_id',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="value_id optional parameter to mark a specific message as read.(value id notificaiton)",
            ),
        ],
        responses={
            200: openapi.Response('Messages marked as read'),
            400: openapi.Response('Service parameter is required')
        }
    )
    def get(self, request, *args, **kwargs):

        user = request.user
        service = request.query_params.get('service')
        value_id = request.query_params.get('value')
        logger.info(f"=========notifications as read: user={user}, service={service}, value_id={value_id}")
        
        # Base queryset with same filtering as UserNotificationListAPIView
        base_queryset = UserNotification.objects.filter(
            user=user,
            notif_data__isnull=False,
            notif_data__has_key='model'
        ).exclude(notif_data={}).exclude(notif_data="")

        if service:
            valid_services = [choice[0] for choice in PushPanel.Service.choices]
            if service not in valid_services:
                return Response({"error": "Invalid service parameter."}, status=400)
            # Mark is_read for messages filtered by service
            user_notifications = base_queryset.filter(service=service)
        # elif value_id:

        else:
            # Mark is_read for all valid messages for the user
            user_notifications = base_queryset

        updated_count = user_notifications.update(is_read=True)

        return Response({
            "message": "PushMessages marked as read.",
            "updated_count": updated_count
        }, status=status.HTTP_200_OK)


class UserNotificationListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserNotificationSerializer

    @swagger_auto_schema(
        tags=['notification'],
        manual_parameters=[
            openapi.Parameter(
                'service',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Specify the service to filter messages (optional).",
                enum=[
                    'hussainiya', 'library', 'talk', 'meet', 'mafatih', 
                    'hadis', 'khatm', 'quran', 'tafsir', 'ahkam', 'coin', 'global',
                ]
            ),            
        ],
    )
    def get(self, request, *args, **kwargs):
        """
            ### Example JSON Response:
            ```json
            {
                "count": 36,
                "next": "https://habibapp.com/account/notif-list/?limit=16&offset=16",
                "previous": null,
                "results": [
                    {
                        "id": 126753,
                        "service": "mafatih",
                        "title": "اللهم عجل لولیک الفرج",
                        "message": "خواهر یا اخوان،",
                        "data": "4450",
                        "image": null,
                        "is_read": true,
                        "created_at": "2024-10-06T14:39:32.893330"
                    }
                ]
            }
            ```
        """        
        return super().get(request, *args, **kwargs)
    
    def get_queryset(self):
        user = self.request.user
        service = self.request.query_params.get('service')

        # Base queryset with filtering for valid notifications
        queryset = UserNotification.objects.filter(
            user=user,
            notif_data__isnull=False,  # notif_data نباید خالی باشد
            notif_data__has_key='model'  # باید دارای کلید model باشد
        ).exclude(
            notif_data={}  # notif_data نباید دیکشنری خالی باشد
        ).exclude(
            notif_data=""  # notif_data نباید رشته خالی باشد
        )

        if service:
            queryset = queryset.filter(service=service)

        return queryset.order_by('-created_at')


class SendNotificationView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['notification'],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'title': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Notification title (optional - if both title and message are omitted, sends data-only notification)'
                ),
                'message': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Notification message (optional - if both title and message are omitted, sends data-only notification)'
                ),
                'data_notif': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description='Additional data to send with notification (can be object or JSON string)'
                ),
                'use_custom_payload': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='If true, all keys from data_notif will be placed directly in the Firebase payload message object',
                    default=False
                ),
            },
            required=[],
            description='''Send notification to authenticated user with flexible payload options:

            1. Regular notification: Provide title and message
            2. Data-only notification: Omit title and message, provide data_notif
            3. Custom payload: Set use_custom_payload=true to send data_notif keys directly in Firebase payload

            When use_custom_payload=true, you can test full FCM payload structure including Android-specific configurations.'''
        ),
        responses={
            200: openapi.Response(
                'Notification sent successfully',
                openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'result': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT),
                            description='FCM response result'
                        )
                    }
                )
            ),
            400: openapi.Response('Bad request - invalid data_notif format'),
            404: openapi.Response('User not found')
        }
    )
    def post(self, request, *args, **kwargs):
        # دریافت fcm_token از کاربر
        user = get_object_or_404(User, id=request.user.id)
        fcm_token = user.fcm

        # دریافت پارامترهای درخواست
        title = request.data.get('title')
        message = request.data.get('message')
        data_notif = request.data.get('data_notif', {})
        use_custom_payload = request.data.get('use_custom_payload', False)

        # تبدیل data_notif به دیکشنری اگر به صورت استرینگ دریافت شده باشد
        import json
        if isinstance(data_notif, str):
            try:
                data_notif = json.loads(data_notif)
                logger.info(f"Converted data_notif string to dictionary: {data_notif}")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse data_notif as JSON: {e}")
                return Response(
                    {"error": "Invalid data_notif format. Must be a valid JSON string."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # اگر use_custom_payload فعال باشد، payload سفارشی ارسال می‌شود
        if use_custom_payload:
            res = send_custom_payload_notification(fcm_token, data_notif)
            logger.info(f"Sent custom payload notification to user {user.id} with payload: {data_notif}")
        elif not title and not message:
            # ارسال نوتیفیکیشن سایلنت (فقط داده)
            res = send_silent_notification([fcm_token], data_notif)
            logger.info(f"Sent silent notification to user {user.id} with data: {data_notif}")
        else:
            # ارسال نوتیفیکیشن معمولی
            res = send_notification([fcm_token], title, message, data_notif)
            logger.info(f"Sent regular notification to user {user.id}: title='{title}', message='{message}'")

        # بازگرداندن پاسخ
        return Response({"result": res}, status=status.HTTP_200_OK)


class ReminderListAPIView(generics.ListAPIView):
    """
    API endpoint for retrieving user reminders with filtering and pagination.
    """
    serializer_class = ReminderSerializer
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['reminders'],
        manual_parameters=[
            openapi.Parameter(
                'service_name',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
                description="Filter reminders by service name (e.g., 'meet', 'dua', etc.)"
            ),
            openapi.Parameter(
                'is_sent',
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="Filter reminders by sent status"
            ),
            openapi.Parameter(
                'is_read',
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="Filter reminders by read status"
            ),
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                required=False,
                description="Filter reminders by active status"
            ),
        ],
        responses={
            200: openapi.Response(
                'List of user reminders',
                ReminderSerializer(many=True)
            )
        }
    )
    def get(self, request, *args, **kwargs):
        """
        Get list of reminders for the authenticated user.

        ### Example JSON Response:
        ```json
        {
            "count": 10,
            "next": "http://example.com/api/reminders/?page=2",
            "previous": null,
            "results": [
                {
                    "id": 1,
                    "service_name": "meet",
                    "object_id": "123",
                    "text": "Don't forget about the meeting tomorrow",
                    "created_at": "2024-01-15T10:30:00Z",
                    "call_time": "2024-01-16T09:00:00Z",
                    "is_sent": false,
                    "is_read": false,
                    "status": true,
                }
            ]
        }
        ```
        """
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        """Filter reminders for the authenticated user with optional filters"""
        queryset = Reminder.objects.filter(user=self.request.user)

        # Apply filters from query parameters
        service_name = self.request.query_params.get('service_name')
        if service_name:
            queryset = queryset.filter(service_name=service_name)

        # is_sent = self.request.query_params.get('is_sent')
        # if is_sent is not None:
        #     is_sent_bool = is_sent.lower() in ('true', '1', 'yes')
        #     queryset = queryset.filter(is_sent=is_sent_bool)

        # is_read = self.request.query_params.get('is_read')
        # if is_read is not None:
        #     is_read_bool = is_read.lower() in ('true', '1', 'yes')
        #     queryset = queryset.filter(is_read=is_read_bool)

        # status = self.request.query_params.get('status')
        # if status is not None:
        #     status_bool = status.lower() in ('true', '1', 'yes')
        #     queryset = queryset.filter(status=status_bool)

        return queryset.order_by('-created_at')


class ReminderUpdateAPIView(generics.UpdateAPIView):
    """
    API endpoint for updating reminder status fields.
    """
    serializer_class = ReminderUpdateSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'

    @swagger_auto_schema(
        tags=['reminders'],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'status': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='General status of the reminder'
                ),
            },
            required=[],
            description='At least one field must be provided'
        ),
        responses={
            200: openapi.Response(
                'Reminder updated successfully',
                ReminderUpdateSerializer
            ),
            400: openapi.Response('Bad request - validation errors'),
            404: openapi.Response('Reminder not found'),
            403: openapi.Response('Permission denied - reminder belongs to another user')
        }
    )
    def patch(self, request, *args, **kwargs):
        """
        Update reminder status fields.

        ### Example Request:
        ```json
        {
            "status": false
        }
        ```

        ### Example Response:
        ```json
        {

            "status": false
        }
        ```
        """
        logger.info(f"ReminderUpdateAPIView PATCH request - path: {request.path}, data: {request.data}")
        return super().patch(request, *args, **kwargs)

    def put(self, request, *args, **kwargs):
        """
        Update reminder status fields (same as PATCH).
        """
        logger.info(f"ReminderUpdateAPIView PUT request - path: {request.path}, data: {request.data}")
        return super().put(request, *args, **kwargs)

    def get_queryset(self):
        """Ensure users can only update their own reminders"""
        return Reminder.objects.filter(user=self.request.user)

    def get_object(self):
        """Get the reminder object and ensure it belongs to the authenticated user"""
        obj = super().get_object()
        return obj